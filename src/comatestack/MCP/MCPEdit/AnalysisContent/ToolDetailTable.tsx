import {Table} from 'antd';
import {ColumnsType} from 'antd/es/table';
import {MCPServerAnalysisData} from '@/types/mcp/mcp';

interface ToolData {
    id: number;
    name: string;
    callCount: number;
    callSuccessRate: number;
}

interface Props {
    data: MCPServerAnalysisData | null;
}

const ToolDetailTable = ({data}: Props) => {
    const columns: ColumnsType<ToolData> = [
        {
            title: '工具名称',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '调用数量',
            dataIndex: 'callCount',
            key: 'callCount',
        },
        {
            title: '调用成功率',
            dataIndex: 'callSuccessRate',
            key: 'callSuccessRate',
            render: (rate: number) => `${rate}%`,
        },
        {
            title: '调用来源',
            key: 'source',
            render: () => '详情',
        },
    ];

    return (
        <Table
            columns={columns}
            dataSource={data?.toolDataList || []}
            rowKey="id"
            pagination={false}
        />
    );
};

export default ToolDetailTable;
