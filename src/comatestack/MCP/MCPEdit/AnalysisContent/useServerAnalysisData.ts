import {useState, useCallback, useEffect} from 'react';
import {apiGetMCPServerData} from '@/api/mcp';
import {MCPServerAnalysisData} from '@/types/mcp/mcp';

export const useServerAnalysisData = (mcpServerId: number) => {
    const [data, setData] = useState<MCPServerAnalysisData | null>(null);
    const [loading, setLoading] = useState(false);

    const loadData = useCallback(
        async () => {
            if (!mcpServerId) {
                return;
            }

            setLoading(true);
            try {
                const result = await apiGetMCPServerData({mcpServerId});
                setData(result);
            } catch (error) {
                console.error('Failed to load server data:', error);
            } finally {
                setLoading(false);
            }
        },
        [mcpServerId]
    );

    useEffect(
        () => {
            loadData();
        },
        [loadData]
    );

    return {data, loading, reload: loadData};
};
